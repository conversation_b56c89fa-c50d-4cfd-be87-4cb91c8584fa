2025-06-14T07:44:41: ==================================================
2025-06-14T07:44:41: 🚀 微信号收集系统后端服务启动成功!
2025-06-14T07:44:41: 📡 服务器运行在: http://localhost:3001
2025-06-14T07:44:41: 🏥 健康检查: http://localhost:3001/api/health
2025-06-14T07:44:41: 📊 管理员登录: admin / ww112233
2025-06-14T07:44:41: 📁 数据文件: F:\wechat\login\backend\data\wechat-accounts.json
2025-06-14T07:44:41: ==================================================
2025-06-14T07:44:49: 2025-06-13T23:44:49.674Z - GET /api/health
2025-06-14T07:44:49: Request body: undefined
2025-06-14T09:36:39: ==================================================
2025-06-14T09:36:39: 🚀 微信号收集系统后端服务启动成功!
2025-06-14T09:36:39: 📡 服务器运行在: http://localhost:3001
2025-06-14T09:36:39: 🏥 健康检查: http://localhost:3001/api/health
2025-06-14T09:36:39: 📊 管理员登录: admin / ww112233
2025-06-14T09:36:39: 📁 数据文件: F:\wechat\login\backend\data\wechat-accounts.json
2025-06-14T09:36:39: ==================================================
2025-06-14T09:36:58: 2025-06-14T01:36:58.801Z - GET /api/health
2025-06-14T09:36:58: Request body: undefined
2025-06-14T17:19:44: ==================================================
2025-06-14T17:19:44: 🚀 微信号收集系统后端服务启动成功!
2025-06-14T17:19:44: 📡 服务器运行在: http://localhost:3001
2025-06-14T17:19:44: 🏥 健康检查: http://localhost:3001/api/health
2025-06-14T17:19:44: 📊 管理员登录: admin / ww112233
2025-06-14T17:19:44: 📁 数据文件: F:\wechat\login\backend\data\wechat-accounts.json
2025-06-14T17:19:44: ==================================================
2025-06-15T10:29:15: ==================================================
2025-06-15T10:29:15: 🚀 微信号收集系统后端服务启动成功!
2025-06-15T10:29:15: 📡 服务器运行在: http://localhost:3001
2025-06-15T10:29:15: 🏥 健康检查: http://localhost:3001/api/health
2025-06-15T10:29:15: 📊 管理员登录: admin / ww112233
2025-06-15T10:29:15: 📁 数据文件: F:\wechat\login\backend\data\wechat-accounts.json
2025-06-15T10:29:15: ==================================================
2025-06-15T10:33:33: 2025-06-15T02:33:33.042Z - POST /api/wechat
2025-06-15T10:33:33: Request body: undefined
2025-06-15T10:33:33: 收到微信号提交请求 - IP: 127.0.0.1, 微信号: mp932是
2025-06-15T10:47:25: 2025-06-15T02:47:25.481Z - POST /api/wechat
2025-06-15T10:47:25: Request body: undefined
2025-06-15T10:47:25: 收到微信号提交请求 - IP: 127.0.0.1, 微信号: dskljpp12390
2025-06-15T10:49:43: 2025-06-15T02:49:43.437Z - POST /api/admin/login
2025-06-15T10:49:43: Request body: undefined
2025-06-15T10:49:43: 管理员登录尝试: { username: 'admin', password: '***' }
2025-06-15T10:49:43: 管理员登录成功
2025-06-15T10:49:43: 2025-06-15T02:49:43.477Z - GET /api/wechat
2025-06-15T10:49:43: Request body: undefined
2025-06-15T10:49:49: 2025-06-15T02:49:49.341Z - DELETE /api/wechat/9411e519-a449-4d4f-ab69-c89676ca2537
2025-06-15T10:49:49: Request body: undefined
2025-06-15T10:49:49: 删除微信号成功: erwer2131231 (ID: 9411e519-a449-4d4f-ab69-c89676ca2537)
2025-06-15T10:49:52: 2025-06-15T02:49:52.181Z - DELETE /api/wechat/b4e97287-2cf3-46a0-ac26-c403de538c35
2025-06-15T10:49:52: Request body: undefined
2025-06-15T10:49:52: 删除微信号成功: fsdfsf21323 (ID: b4e97287-2cf3-46a0-ac26-c403de538c35)
2025-06-15T10:49:53: 2025-06-15T02:49:53.959Z - DELETE /api/wechat/e36f120d-fc68-4bd4-be71-2d0181b74efe
2025-06-15T10:49:53: Request body: undefined
2025-06-15T10:49:53: 删除微信号成功: fdsfswr345354 (ID: e36f120d-fc68-4bd4-be71-2d0181b74efe)
2025-06-15T10:49:56: 2025-06-15T02:49:56.927Z - DELETE /api/wechat/fe38043c-5d9f-48a9-ac35-b23a56a514ef
2025-06-15T10:49:56: Request body: undefined
2025-06-15T10:49:56: 删除微信号成功: fsfsfsfseff124332323 (ID: fe38043c-5d9f-48a9-ac35-b23a56a514ef)
2025-06-15T10:49:59: 2025-06-15T02:49:59.374Z - DELETE /api/wechat/1c615a60-38db-4982-9152-60c5f7b757f7
2025-06-15T10:49:59: Request body: undefined
2025-06-15T10:49:59: 删除微信号成功: qwdwer213123 (ID: 1c615a60-38db-4982-9152-60c5f7b757f7)
2025-06-15T10:50:00: 2025-06-15T02:50:00.914Z - DELETE /api/wechat/855404f8-4c2b-4363-b9de-138f943c41bc
2025-06-15T10:50:00: Request body: undefined
2025-06-15T10:50:00: 删除微信号成功: feretrt456456 (ID: 855404f8-4c2b-4363-b9de-138f943c41bc)
2025-06-15T10:50:02: 2025-06-15T02:50:02.228Z - DELETE /api/wechat/f6e4607b-e787-47ca-ae1f-5e804a9cec25
2025-06-15T10:50:02: Request body: undefined
2025-06-15T10:50:02: 删除微信号成功: mp932是 (ID: f6e4607b-e787-47ca-ae1f-5e804a9cec25)
2025-06-15T10:50:03: 2025-06-15T02:50:03.829Z - DELETE /api/wechat/ea33e997-a0ef-44b7-8100-65d99d8ac8b9
2025-06-15T10:50:03: Request body: undefined
2025-06-15T10:50:03: 删除微信号成功: dskljpp12390 (ID: ea33e997-a0ef-44b7-8100-65d99d8ac8b9)
2025-06-15T11:21:16: 2025-06-15T03:21:16.551Z - POST /api/admin/login
2025-06-15T11:21:16: Request body: undefined
2025-06-15T11:21:16: 管理员登录尝试: { username: 'admin', password: '***' }
2025-06-15T11:21:16: 管理员登录成功
2025-06-15T11:21:16: 2025-06-15T03:21:16.594Z - GET /api/wechat
2025-06-15T11:21:16: Request body: undefined
2025-06-15T11:24:38: 2025-06-15T03:24:38.869Z - POST /api/admin/login
2025-06-15T11:24:38: Request body: undefined
2025-06-15T11:24:38: 管理员登录尝试: { username: 'admin', password: '***' }
2025-06-15T11:24:38: 管理员登录成功
2025-06-15T11:24:38: 2025-06-15T03:24:38.906Z - GET /api/wechat
2025-06-15T11:24:38: Request body: undefined
2025-06-15T11:39:25: ==================================================
2025-06-15T11:39:25: 🚀 微信号收集系统后端服务启动成功!
2025-06-15T11:39:25: 📡 服务器运行在: http://localhost:3001
2025-06-15T11:39:25: 🏥 健康检查: http://localhost:3001/api/health
2025-06-15T11:39:25: 📊 默认超级管理员: admin / ww112233
2025-06-15T11:39:25: 📁 数据文件: F:\wechat\login\backend\data\wechat-accounts.json
2025-06-15T11:39:25: ==================================================
2025-06-15T11:40:16: 2025-06-15T03:40:16.712Z - POST /api/admin/login
2025-06-15T11:40:16: Request body: undefined
2025-06-15T11:40:16: 管理员登录尝试: { username: 'admin', password: '***' }
2025-06-15T11:40:16: 管理员登录成功: admin
2025-06-15T11:40:16: 2025-06-15T03:40:16.772Z - GET /api/wechat
2025-06-15T11:40:16: Request body: undefined
2025-06-15T11:40:18: 2025-06-15T03:40:18.239Z - GET /api/admin/users
2025-06-15T11:40:18: Request body: undefined
2025-06-15T11:40:20: 2025-06-15T03:40:20.126Z - GET /api/wechat
2025-06-15T11:40:20: Request body: undefined
2025-06-15T11:40:23: 2025-06-15T03:40:23.677Z - GET /api/admin/users
2025-06-15T11:40:23: Request body: undefined
2025-06-15T11:40:25: 2025-06-15T03:40:25.974Z - GET /api/admin/users
2025-06-15T11:40:25: Request body: undefined
2025-06-15T11:40:29: 2025-06-15T03:40:29.824Z - GET /api/wechat
2025-06-15T11:40:29: Request body: undefined
2025-06-15T11:40:36: 2025-06-15T03:40:36.819Z - GET /api/admin/users
2025-06-15T11:40:36: Request body: undefined
2025-06-15T11:40:49: 2025-06-15T03:40:49.478Z - GET /api/wechat
2025-06-15T11:40:49: Request body: undefined
2025-06-15T11:41:07: 2025-06-15T03:41:07.152Z - POST /api/admin/login
2025-06-15T11:41:07: Request body: undefined
2025-06-15T11:41:07: 管理员登录尝试: { username: 'admin', password: '***' }
2025-06-15T11:41:07: 管理员登录成功: admin
2025-06-15T11:41:07: 2025-06-15T03:41:07.193Z - GET /api/wechat
2025-06-15T11:41:07: Request body: undefined
2025-06-15T11:41:10: 2025-06-15T03:41:10.308Z - GET /api/wechat
2025-06-15T11:41:10: Request body: undefined
2025-06-15T11:41:25: 2025-06-15T03:41:25.925Z - GET /api/admin/users
2025-06-15T11:41:25: Request body: undefined
2025-06-15T11:53:01: ==================================================
2025-06-15T11:53:01: 🚀 微信号收集系统后端服务启动成功!
2025-06-15T11:53:01: 📡 服务器运行在: http://localhost:3001
2025-06-15T11:53:01: 🏥 健康检查: http://localhost:3001/api/health
2025-06-15T11:53:01: 📊 默认超级管理员: admin / ww112233
2025-06-15T11:53:01: 📁 数据文件: F:\wechat\login\backend\data\wechat-accounts.json
2025-06-15T11:53:01: ==================================================
2025-06-15T11:54:49: 2025-06-15T03:54:49.574Z - POST /api/admin/login
2025-06-15T11:54:49: Request body: undefined
2025-06-15T11:54:49: 管理员登录尝试: { username: 'admin', password: '***' }
2025-06-15T11:54:49: 管理员登录成功: admin
2025-06-15T11:54:49: 2025-06-15T03:54:49.632Z - GET /api/wechat
2025-06-15T11:54:49: Request body: undefined
2025-06-15T11:54:51: 2025-06-15T03:54:51.050Z - GET /api/admin/keys
2025-06-15T11:54:51: Request body: undefined
2025-06-15T11:54:52: 2025-06-15T03:54:52.265Z - GET /api/admin/keys
2025-06-15T11:54:52: Request body: undefined
2025-06-15T11:54:52: 2025-06-15T03:54:52.878Z - GET /api/admin/keys
2025-06-15T11:54:52: Request body: undefined
2025-06-15T12:09:29: ==================================================
2025-06-15T12:09:29: 🚀 微信号收集系统后端服务启动成功!
2025-06-15T12:09:29: 📡 服务器运行在: http://localhost:3001
2025-06-15T12:09:29: 🏥 健康检查: http://localhost:3001/api/health
2025-06-15T12:09:29: 📊 默认超级管理员: admin / ww112233
2025-06-15T12:09:29: 📁 数据文件: F:\wechat\login\backend\data\wechat-accounts.json
2025-06-15T12:09:29: ==================================================
2025-06-15T12:11:48: 2025-06-15T04:11:48.909Z - GET /api/wechat
2025-06-15T12:11:48: Request body: undefined
2025-06-15T12:11:51: 2025-06-15T04:11:51.146Z - GET /api/admin/keys
2025-06-15T12:11:51: Request body: undefined
2025-06-15T12:12:34: ==================================================
2025-06-15T12:12:34: 🚀 微信号收集系统后端服务启动成功!
2025-06-15T12:12:34: 📡 服务器运行在: http://localhost:3001
2025-06-15T12:12:34: 🏥 健康检查: http://localhost:3001/api/health
2025-06-15T12:12:34: 📊 默认超级管理员: admin / ww112233
2025-06-15T12:12:34: 📁 数据文件: F:\wechat\login\backend\data\wechat-accounts.json
2025-06-15T12:12:34: ==================================================
